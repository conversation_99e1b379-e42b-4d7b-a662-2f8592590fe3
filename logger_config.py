"""
日志配置模块
提供统一的日志配置和管理功能
"""

import logging
import logging.handlers
import os
import yaml
from datetime import datetime
from typing import Optional

class LoggerConfig:
    """日志配置管理器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化日志配置
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.log_dir = self.config['paths']['logs']
        self._ensure_log_directory()
        self._setup_logging()
    
    def _load_config(self, config_path: str) -> dict:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            配置字典
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except Exception as e:
            print(f"配置文件加载失败: {e}")
            raise
    
    def _ensure_log_directory(self):
        """确保日志目录存在"""
        try:
            if not os.path.exists(self.log_dir):
                os.makedirs(self.log_dir)
                print(f"创建日志目录: {self.log_dir}")
        except Exception as e:
            print(f"创建日志目录失败: {e}")
            raise
    
    def _setup_logging(self):
        """设置日志配置"""
        try:
            log_config = self.config['logging']
            
            # 生成日志文件名
            timestamp = datetime.now().strftime(self.config['system']['timestamp_format'])
            log_filename = f"{timestamp}_处理日志.log"
            log_filepath = os.path.join(self.log_dir, log_filename)
            
            # 配置根日志器
            logging.basicConfig(
                level=getattr(logging, log_config['level']),
                format=log_config['format'],
                handlers=[
                    # 控制台处理器
                    logging.StreamHandler(),
                    # 文件处理器（带轮转）
                    logging.handlers.RotatingFileHandler(
                        log_filepath,
                        maxBytes=self._parse_size(log_config.get('max_file_size', '10MB')),
                        backupCount=log_config.get('backup_count', 5),
                        encoding='utf-8'
                    )
                ]
            )
            
            # 设置第三方库日志级别
            logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
            logging.getLogger('cx_Oracle').setLevel(logging.WARNING)
            
            print(f"日志系统初始化成功，日志文件: {log_filepath}")
            
        except Exception as e:
            print(f"日志系统初始化失败: {e}")
            raise
    
    def _parse_size(self, size_str: str) -> int:
        """
        解析大小字符串为字节数
        
        Args:
            size_str: 大小字符串，如 "10MB"
            
        Returns:
            字节数
        """
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def get_logger(self, name: str) -> logging.Logger:
        """
        获取指定名称的日志器
        
        Args:
            name: 日志器名称
            
        Returns:
            日志器实例
        """
        return logging.getLogger(name)


class ProcessLogger:
    """处理过程日志记录器"""
    
    def __init__(self, process_name: str):
        """
        初始化处理日志记录器
        
        Args:
            process_name: 处理过程名称
        """
        self.process_name = process_name
        self.logger = logging.getLogger(f"process.{process_name}")
        self.start_time = datetime.now()
        self.step_count = 0
        
    def log_start(self, description: str = ""):
        """
        记录处理开始
        
        Args:
            description: 处理描述
        """
        self.logger.info(f"{'='*50}")
        self.logger.info(f"开始执行: {self.process_name}")
        if description:
            self.logger.info(f"处理描述: {description}")
        self.logger.info(f"开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.info(f"{'='*50}")
    
    def log_step(self, step_name: str, details: str = "", success: bool = True):
        """
        记录处理步骤
        
        Args:
            step_name: 步骤名称
            details: 步骤详情
            success: 是否成功
        """
        self.step_count += 1
        status = "✓" if success else "✗"
        self.logger.info(f"步骤 {self.step_count}: {status} {step_name}")
        if details:
            self.logger.info(f"  详情: {details}")
    
    def log_error(self, error_msg: str, exception: Optional[Exception] = None):
        """
        记录错误信息
        
        Args:
            error_msg: 错误消息
            exception: 异常对象
        """
        self.logger.error(f"❌ 错误: {error_msg}")
        if exception:
            self.logger.error(f"异常详情: {str(exception)}")
    
    def log_warning(self, warning_msg: str):
        """
        记录警告信息
        
        Args:
            warning_msg: 警告消息
        """
        self.logger.warning(f"⚠️ 警告: {warning_msg}")
    
    def log_end(self, success: bool = True, summary: str = ""):
        """
        记录处理结束
        
        Args:
            success: 是否成功完成
            summary: 处理总结
        """
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        self.logger.info(f"{'='*50}")
        status = "成功完成" if success else "执行失败"
        self.logger.info(f"处理结果: {status}")
        self.logger.info(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.info(f"总耗时: {duration}")
        self.logger.info(f"执行步骤: {self.step_count}")
        if summary:
            self.logger.info(f"处理总结: {summary}")
        self.logger.info(f"{'='*50}")


# 全局日志配置实例
logger_config = None

def init_logging(config_path: str = "config.yaml") -> LoggerConfig:
    """
    初始化日志系统
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        日志配置实例
    """
    global logger_config
    if logger_config is None:
        logger_config = LoggerConfig(config_path)
    return logger_config

def get_logger(name: str) -> logging.Logger:
    """
    获取日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        日志器实例
    """
    if logger_config is None:
        init_logging()
    return logger_config.get_logger(name)

def get_process_logger(process_name: str) -> ProcessLogger:
    """
    获取处理过程日志器
    
    Args:
        process_name: 处理过程名称
        
    Returns:
        处理过程日志器实例
    """
    if logger_config is None:
        init_logging()
    return ProcessLogger(process_name)
